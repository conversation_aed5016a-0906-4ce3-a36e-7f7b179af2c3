/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity; :com.example.myapplication.domain.repository.AuthRepository0 /com.example.myapplication.domain.model.Resource0 /com.example.myapplication.domain.model.Resource0 /com.example.myapplication.domain.model.Resource/ .com.example.myapplication.ui.login.LoginResult/ .com.example.myapplication.ui.login.LoginResult. -com.example.myapplication.ui.login.LoginEvent. -com.example.myapplication.ui.login.LoginEvent. -com.example.myapplication.ui.login.LoginEvent. -com.example.myapplication.ui.login.LoginEvent. -com.example.myapplication.ui.login.LoginEvent androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.Factory; :com.example.myapplication.domain.repository.AuthRepository; :com.example.myapplication.domain.repository.AuthRepository; :com.example.myapplication.domain.repository.AuthRepository; :com.example.myapplication.domain.repository.AuthRepository; :com.example.myapplication.domain.repository.AuthRepository; :com.example.myapplication.domain.repository.AuthRepository